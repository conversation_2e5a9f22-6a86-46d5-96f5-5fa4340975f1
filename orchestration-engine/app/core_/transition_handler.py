import asyncio
import json
import time
import traceback
from typing import Any, List, Dict
from app.utils.helper_functions import format_execution_result, safe_list_convert
from app.utils.enhanced_logger import get_logger
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.node_executor import NodeExecutor
from app.services.loop_executor import LoopExecutor
from app.services.workflow_executor import WorkflowExecutor
from app.core_.conditional_routing_handler import ConditionalRoutingHandler

logger = get_logger("TransitionHandler")


class TransitionHandler:

    def __init__(
        self,
        state_manager,
        transitions_by_id,
        nodes,
        dependency_map,
        workflow_utils,
        tool_executor,
        node_executor=None,
        agent_executor=None,
        workflow_executor=None,
        result_callback=None,
        approval=False,
        user_id=None,
    ):
        self.state_manager = state_manager
        self.transitions_by_id = transitions_by_id
        self.nodes = nodes
        self.result_callback = result_callback
        self.dependency_map = dependency_map
        self.workflow_utils = workflow_utils
        self.tool_executor = tool_executor  # MCP server executor
        self.node_executor = node_executor  # Node executor for Components
        self.agent_executor = agent_executor  # Agent executor for Agent tasks
        self.workflow_executor = (
            workflow_executor  # Workflow executor for Workflow tasks
        )
        self.user_id = user_id  # User ID for tool execution

        # Flags
        self.current_transition_id = None
        self.approval = approval
        self.workflow_paused = False

        # Initialize conditional routing handler
        self.conditional_routing_handler = ConditionalRoutingHandler(logger=logger)
        self._pause_event = asyncio.Event()

        # Orchestration engine reference for loop coordination
        self.orchestration_engine = None

        self.logger = logger

        # Initialize completion callback system for loop body chain integration
        self._completion_callbacks = {}  # transition_id -> callback function
        self._active_loop_executors = {}  # transition_id -> loop_executor instance

        # Initialize loop execution stack for tracking nested/concurrent loops
        self._loop_execution_stack = (
            []
        )  # Stack of loop configurations for filtering entry transitions

        self.logger.info("TransitionHandler initialized")

    def _find_initial_transition(self):
        """
        Returns a list of transitions with transition_type='initial' if present,
        otherwise a list of all transitions sorted by sequence number.

        This allows for multiple initial transitions to be executed in parallel
        when multiple transitions have transition_type='initial'.
        """
        initial_transitions = [
            s
            for s in self.transitions_by_id.values()
            if s["transition_type"] == "initial"
        ]
        if initial_transitions:
            return initial_transitions

        # If no initial transitions found, use the transition with the smallest sequence number
        if self.transitions_by_id:
            return [
                sorted(self.transitions_by_id.values(), key=lambda s: s["sequence"])[0]
            ]

        raise Exception("No initial transitions found")

    async def _execute_transition_with_tracking(self, transition):
        """
        Execute a transition with proper tracking and monitoring to ensure it completes.
        This wrapper helps identify and debug issues with parallel execution.
        """

        transition_id = transition["id"]
        try:
            self.logger.info(
                f"Starting parallel execution of transition: {transition_id}"
            )
            result_info = {
                "result": f"Starting execution of transition: {transition_id}",
                "message": "Starting execution...",
                "transition_id": transition_id,
                "status": "started",
            }
            await self.result_callback(result_info)
            start_time = asyncio.get_event_loop().time()

            # Execute the transition
            result = await self._execute_standard_or_reflection_transition(transition)

            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            self.logger.info(
                f"Completed transition {transition_id} in {execution_time:.2f} seconds"
            )
            result_info = {
                "result": f"Completed transition in {execution_time:.2f} seconds",
                "message": f"Transition completed in {execution_time:.2f} seconds",
                "transition_id": transition_id,
                "status": "time_logged",
            }
            await self.result_callback(result_info)
            # Verify the transition was properly marked as completed
            if transition_id not in self.state_manager.completed_transitions:
                self.logger.warning(
                    f"Transition {transition_id} executed but not marked as completed!"
                )

            # 🔍 DEBUG: Log what we're returning to orchestration engine
            self.logger.debug(
                f"🔄 _execute_transition_with_tracking returning for {transition_id}: {result}"
            )
            self.logger.debug(
                f"🔄 Return type: {type(result)}, length: {len(result) if isinstance(result, list) else 'not list'}"
            )
            return result
        except Exception as e:
            self.logger.error(f"Exception in transition {transition_id}: {str(e)}")
            raise Exception(f"Exception in transition {transition_id}: {str(e)}")

    async def _execute_standard_or_reflection_transition(
        self, transition: dict, server_params_override=None, action_type=None
    ) -> list[str]:
        """
        Executes a 'standard' or 'reflection' transition.
        Assumes each transition in this function executes exactly one node.
        Returns the next transition IDs and the result of the execution.
        """
        transition_id = transition["id"]
        transition_type = transition["transition_type"]
        execution_type = transition.get(
            "execution_type", "MCP"
        )  # Default to MCP for backward compatibility
        self.logger.execute(
            f"Transition '{transition_id}' (type={transition_type}, execution_type={execution_type})"
        )

        node_info = transition.get("node_info", {})
        if not node_info:
            self.logger.error(
                f"No node_info specified for transition '{transition_id}'"
            )
            raise AttributeError(
                f"No node_info specified for transition '{transition_id}'"
            )

        node_id = node_info.get("node_id")
        node_label = transition.get(
            "node_label", ""
        )  # Extract node_label from transition
        tools_to_use = node_info.get("tools_to_use", [])
        output_data_configs = node_info.get("output_data", [])
        input_data_configs = node_info.get("input_data", [])
        next_transitions = []

        node_details = self.nodes.get(node_id)
        if not node_details:
            self.logger.error(f"Node details for '{node_id}' not found in schema")
            raise AttributeError(f"Node details for '{node_id}' not found in schema")

        server_tools_data = node_details.get("server_tools", [])
        if not server_tools_data:
            self.logger.error(f"No server tools defined for node '{node_id}'")
            raise AttributeError(f"No server tools defined for node '{node_id}'")

        tool_results = {}
        execution_result = None
        approval_required = transition.get("approval_required", False)

        # Determine which executor to use based on execution_type
        executor = self._get_executor_for_type(execution_type, transition)
        if not executor:
            self.logger.error(
                f"No executor available for execution type: {execution_type}"
            )
            raise ValueError(
                f"No executor available for execution type: {execution_type}"
            )

        # Log which executor is being used
        if executor == self.node_executor:
            executor_type = "NodeExecutor"
        elif executor == self.agent_executor:
            executor_type = "AgentExecutor"
        elif executor == self.workflow_executor:
            executor_type = "WorkflowExecutor"
        else:
            executor_type = "KafkaToolExecutor"
        self.logger.info(f"Using {executor_type} for execution_type: {execution_type}")

        for tool_config in tools_to_use:
            tool_id = tool_config.get("tool_id")
            tool_name = tool_config.get("tool_name")
            tool_params_config = tool_config.get("tool_params", {"items": []}).get(
                "items"
            )

            node_tool_info = next(
                (tool for tool in server_tools_data if tool["tool_name"] == tool_name),
                None,
            )
            if not node_tool_info:
                self.logger.error(
                    f"Tool with name '{tool_name}' not found for node '{node_id}'"
                )
                node_tool_info = next(
                    (tool for tool in server_tools_data if tool["tool_id"] == tool_id),
                    None,
                )
                if not node_tool_info:
                    self.logger.error(
                        f"Tool with id '{tool_id}' not found for node '{node_id}'"
                    )
                    continue

            result_info = {
                "transition_id": transition_id,
                "node_label": node_label,  # Use node_label instead of node_id
                "tool_name": tool_name,
                "message": "",
                "result": None,
            }
            try:
                # Use universal handle-based parameter resolution
                tool_parameters = await self._resolve_tool_parameters_universally(
                    node_tool_info,
                    input_data_configs,
                    transition_id,
                    tool_params_config,
                    transition,
                )
                self.logger.debug(f"tool Parameters: {tool_parameters}")
                self.logger.info(
                    f"Invoking tool '{tool_name}' (tool_id: {tool_id}) for node '{node_id}' in transition '{transition_id}' with parameters: {tool_parameters}"
                )
                result_info["result"] = (
                    f"Connecting to server {node_label if node_label else node_id}"
                )
                result_info["message"] = f"Connecting to server"
                result_info["status"] = "connecting"
                await self.result_callback(result_info)
                if server_params_override:

                    def find_and_update_parameter(
                        param_dict, param_name, override_value, path=""
                    ):
                        """Recursively search for parameter in nested dictionaries and update if found."""
                        found = False

                        if isinstance(param_dict, dict):

                            if param_name in param_dict:
                                param_dict[param_name] = override_value
                                self.logger.debug(
                                    f"Parameter '{path+param_name}' for transition '{transition_id}' overridden with value: {override_value}"
                                )
                                found = True

                            for key, value in param_dict.items():
                                if isinstance(value, dict):
                                    nested_found = find_and_update_parameter(
                                        value,
                                        param_name,
                                        override_value,
                                        f"{path}{key}.",
                                    )
                                    found = found or nested_found

                        return found

                    for param_name, override_value in server_params_override.items():
                        found = find_and_update_parameter(
                            tool_parameters, param_name, override_value
                        )

                        if not found:
                            self.logger.warning(
                                f"Parameter '{param_name}' from server_params_override not found in tool_parameters for transition '{transition_id}'. Ignoring override."
                            )

                # Execute the tool using the appropriate executor based on execution_type
                if execution_type == "loop":
                    # For Loop executor, pass the loop configuration
                    loop_config = transition.get("loop_config")
                    if not loop_config:
                        raise ValueError(
                            f"Loop configuration missing for loop transition: {transition_id}"
                        )

                    # Use loop_config directly (parameter resolution will be handled by LoopExecutor)
                    resolved_loop_config = loop_config

                    # Update loop config with resolved iteration_list from tool parameters
                    if tool_parameters and "iteration_list" in tool_parameters:
                        iteration_source = resolved_loop_config.get(
                            "iteration_source", {}
                        )
                        if "iteration_list" in iteration_source:
                            # Convert iteration_list using safe_list_convert to handle JSON strings
                            iteration_list_value = safe_list_convert(
                                tool_parameters["iteration_list"], []
                            )

                            # Update the iteration_list in loop config with resolved data
                            iteration_source["iteration_list"] = iteration_list_value
                            self.logger.debug(
                                f"🔧 Updated loop config iteration_list with resolved data: {iteration_list_value}"
                            )

                    # Loop body configuration will be handled by LoopExecutor

                    # Prepare input data from resolved tool parameters for loop executor
                    loop_input_data = tool_parameters if tool_parameters else {}

                    # Prepare output routing from output data configs
                    loop_output_routing = {"output_configs": output_data_configs}

                    # Check if executor is actually a LoopExecutor
                    if hasattr(executor, "set_orchestration_engine"):
                        # This is a LoopExecutor, pass loop-specific parameters
                        execution_result = await executor.execute_tool(
                            tool_name=tool_name,
                            tool_parameters=tool_parameters,
                            loop_config=resolved_loop_config,
                            transition_id=transition_id,
                            input_data=loop_input_data,
                            output_routing=loop_output_routing,
                            input_data_configs=input_data_configs,
                            node_label=node_label,
                        )
                    else:
                        # This is a fallback executor (KafkaToolExecutor), use standard parameters
                        execution_result = await executor.execute_tool(
                            tool_name=tool_name,
                            tool_parameters=tool_parameters,
                            mcp_id=node_id,
                            transition_id=transition_id,
                        )
                elif executor == self.tool_executor:
                    # Validate user_id is available for MCP tool execution
                    if not self.user_id:
                        self.logger.error(
                            f"user_id is required for MCP tool execution but not provided"
                        )
                        raise ValueError("user_id is required for tool execution")

                    # For MCP executor, pass the node_id as mcp_id (no server_script_path needed)
                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        mcp_id=node_id,
                        transition_id=transition_id,
                        node_label=node_label,
                    )
                elif executor == self.agent_executor:
                    # Validate user_id is available for agent tool execution
                    if not self.user_id:
                        self.logger.error(
                            f"user_id is required for agent tool execution but not provided"
                        )
                        raise ValueError("user_id is required for tool execution")

                    # For Agent executor, pass the node_id as agent_id (no server_script_path needed)
                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        transition_id=transition_id,
                        node_label=node_label,
                    )
                elif executor == self.workflow_executor:
                    # Validate user_id is available for workflow tool execution
                    if not self.user_id:
                        self.logger.error(
                            f"user_id is required for workflow tool execution but not provided"
                        )
                        raise ValueError("user_id is required for tool execution")

                    # Set user_id and correlation_id for workflow executor
                    executor.set_user_id(self.user_id)
                    if hasattr(self, "correlation_id") and self.correlation_id:
                        executor.set_correlation_id(self.correlation_id)

                    # For Workflow executor, pass input_data_configs for handle mapping resolution
                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        transition_id=transition_id,
                        node_label=node_label,
                        input_data_configs=input_data_configs,
                    )
                else:
                    # Validate user_id is available for other tool execution
                    if not self.user_id:
                        self.logger.error(
                            f"user_id is required for tool execution but not provided"
                        )
                        raise ValueError("user_id is required for tool execution")

                    # For other executors (like NodeExecutor), use the original signature (no server_script_path needed)
                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        transition_id=transition_id,
                        node_label=node_label,
                    )

                # Log the execution result for debugging (safe serialization)
                try:
                    result_str = json.dumps(execution_result, indent=2)
                except (ValueError, TypeError) as e:
                    # Handle circular references or non-serializable objects
                    result_str = (
                        f"<Non-serializable result: {type(execution_result).__name__}>"
                    )
                    if hasattr(execution_result, "__dict__"):
                        try:
                            # Try to serialize just the basic attributes
                            basic_attrs = {
                                k: v
                                for k, v in execution_result.__dict__.items()
                                if isinstance(
                                    v, (str, int, float, bool, list, dict, type(None))
                                )
                            }
                            result_str = json.dumps(basic_attrs, indent=2)
                        except:
                            result_str = (
                                f"<Complex object: {type(execution_result).__name__}>"
                            )

                self.logger.info(
                    f"Execution result from {execution_type} executor: {result_str}"
                )

                # Check for errors in the execution result
                if isinstance(execution_result, dict):
                    # Log the execution result for debugging (safe serialization)
                    try:
                        result_str = json.dumps(execution_result, indent=2)
                    except (ValueError, TypeError):
                        # Handle circular references or non-serializable objects
                        result_str = f"<Non-serializable result: {type(execution_result).__name__}>"

                    self.logger.info(
                        f"Checking execution result for errors: {result_str}"
                    )

                    # Check if the status is error
                    if execution_result.get("status") == "error":
                        # Handle different error formats from different executors
                        error_message = None

                        # Format 1: Direct error message in 'error' field (from kafka_tool_executor.py)
                        if "error" in execution_result:
                            error_message = execution_result.get("error")
                            self.logger.info(
                                f"Found direct error in execution result: {error_message}"
                            )

                        # Format 2: Error message in nested 'result.error' field (from node_executor.py)
                        elif isinstance(execution_result.get("result"), dict):
                            result_dict = execution_result["result"]
                            self.logger.info(
                                f"Processing result dictionary: {json.dumps(result_dict, indent=2)}"
                            )

                            # Extract error message from result.error
                            if "error" in result_dict:
                                error_message = result_dict.get("error")
                                self.logger.info(
                                    f"Found error in result dictionary: {error_message}"
                                )

                                # Include additional error details if available
                                if "data" in result_dict and isinstance(
                                    result_dict["data"], dict
                                ):
                                    error_details = result_dict["data"].get("detail")
                                    if error_details:
                                        error_message = (
                                            f"{error_message} - {error_details}"
                                        )
                                        self.logger.info(
                                            f"Added error details: {error_message}"
                                        )

                                # Include status code if available
                                if "status_code" in result_dict:
                                    status_code = result_dict.get("status_code")
                                    if (
                                        status_code
                                        and not str(status_code) in error_message
                                    ):
                                        error_message = f"{error_message} (Status Code: {status_code})"
                                        self.logger.info(
                                            f"Added status code: {error_message}"
                                        )

                        # Fallback if no specific error format is matched
                        else:
                            error_message = execution_result.get(
                                "result", "Unknown error occurred"
                            )
                            self.logger.info(
                                f"Using fallback error message: {error_message}"
                            )

                        if error_message:
                            self.logger.error(
                                f"Execution failed with error: {error_message}"
                            )
                            # Force an exception to be raised for any error
                            raise Exception(f"Tool execution error: {error_message}")
                        else:
                            # Safe serialization for error logging
                            try:
                                result_str = json.dumps(execution_result, indent=2)
                            except (ValueError, TypeError):
                                result_str = f"<Non-serializable result: {type(execution_result).__name__}>"

                            self.logger.error(
                                f"Execution failed but no error message could be extracted: {result_str}"
                            )
                            raise Exception(
                                f"Tool execution error: Unknown error occurred"
                            )

                    # Special handling for Components execution type
                    elif execution_type == "Components" and isinstance(
                        execution_result.get("result"), dict
                    ):
                        result_dict = execution_result["result"]
                        # Check if there's an error field in the result dict even if status is not 'error'
                        if "error" in result_dict:
                            error_message = result_dict.get("error")
                            self.logger.info(
                                f"Found error in Components result: {error_message}"
                            )

                            # Include additional error details if available
                            if "data" in result_dict and isinstance(
                                result_dict["data"], dict
                            ):
                                error_details = result_dict["data"].get("detail")
                                if error_details:
                                    error_message = f"{error_message} - {error_details}"

                            # Include status code if available
                            if "status_code" in result_dict:
                                status_code = result_dict.get("status_code")
                                if (
                                    status_code
                                    and not str(status_code) in error_message
                                ):
                                    error_message = (
                                        f"{error_message} (Status Code: {status_code})"
                                    )

                            self.logger.error(
                                f"Components execution failed with error: {error_message}"
                            )
                            raise Exception(f"Tool execution error: {error_message}")

                # Wrap execution result in the expected structure for handle mapping compatibility
                wrapped_result = {
                    "transition_id": transition_id,
                    "node_label": node_label,  # Use node_label instead of node_id
                    "tool_name": tool_name,
                    "result": {"result": execution_result},
                    "status": "completed",
                    "timestamp": time.time(),
                }
                tool_results[tool_name] = wrapped_result

                output_schema = node_tool_info.get("output_schema", {})
                if execution_type == "MCP":
                    try:
                        # Extract the actual content from MCP response structure
                        # MCP results typically have structure: [{"content": [...], "isError": false}]
                        if (
                            isinstance(execution_result, list)
                            and len(execution_result) > 0
                        ):
                            mcp_result = execution_result[0]
                            if isinstance(mcp_result, dict) and "content" in mcp_result:
                                # Extract content from MCP response
                                content_data = mcp_result["content"]
                                if (
                                    isinstance(content_data, list)
                                    and len(content_data) > 0
                                ):
                                    # Get the text content from the first content item
                                    first_content = content_data[0]
                                    if (
                                        isinstance(first_content, dict)
                                        and "text" in first_content
                                    ):
                                        actual_content = first_content["text"]
                                    else:
                                        actual_content = content_data
                                else:
                                    actual_content = content_data

                                # Format in the same structure as agent responses
                                formatted_result = {
                                    "data": actual_content,
                                    "data_type": "string",
                                    "semantic_type": "string",
                                    "property_name": "content",
                                }
                            else:
                                # Fallback to original formatting if structure is different
                                formatted_result = format_execution_result(
                                    output_schema, execution_result
                                )
                        else:
                            # Fallback to original formatting
                            formatted_result = format_execution_result(
                                output_schema, execution_result
                            )
                    except Exception as e:
                        self.logger.error(f"Error processing MCP result: {e}")
                        # Fallback to original formatting
                        formatted_result = format_execution_result(
                            output_schema, execution_result
                        )

                    # Log semantic type inclusion for debugging
                    self.logger.debug(f"Formatted MCP result: {formatted_result}")
                elif execution_type == "agent":
                    # Agent executor returns the content string directly (not wrapped in agent_response)
                    # execution_result is the raw content from agent_response.get("content")
                    agent_content = execution_result

                    self.logger.debug(
                        f"Processing agent content: {type(agent_content)} - {str(agent_content)[:200]}..."
                    )

                    # Check if agent_content is stringified JSON
                    if isinstance(agent_content, str) and agent_content.strip():
                        try:
                            # Check if content is wrapped in markdown code blocks
                            content_to_parse = agent_content.strip()
                            self.logger.debug(
                                f"Original content length: {len(content_to_parse)}, starts with: {content_to_parse[:20]}, ends with: {content_to_parse[-20:]}"
                            )

                            if content_to_parse.startswith("```json"):
                                # Find the closing ``` and extract everything between
                                if content_to_parse.endswith("```"):
                                    # Remove ```json from start and ``` from end, handling newlines properly
                                    start_marker = content_to_parse.find(
                                        "\n", 7
                                    )  # Find first newline after ```json
                                    if start_marker != -1:
                                        content_to_parse = content_to_parse[
                                            start_marker + 1 : -3
                                        ].strip()
                                    else:
                                        content_to_parse = content_to_parse[
                                            7:-3
                                        ].strip()
                                    self.logger.debug(
                                        f"Extracted JSON from ```json markdown blocks, new length: {len(content_to_parse)}"
                                    )
                            elif content_to_parse.startswith(
                                "```"
                            ) and content_to_parse.endswith("```"):
                                # Handle generic code blocks that might contain JSON
                                start_marker = content_to_parse.find(
                                    "\n", 3
                                )  # Find first newline after ```
                                if start_marker != -1:
                                    content_to_parse = content_to_parse[
                                        start_marker + 1 : -3
                                    ].strip()
                                else:
                                    content_to_parse = content_to_parse[3:-3].strip()
                                self.logger.debug(
                                    f"Extracted content from generic ``` blocks, new length: {len(content_to_parse)}"
                                )

                            # Attempt to parse as JSON
                            parsed_json = json.loads(content_to_parse)
                            self.logger.debug(
                                f"Successfully parsed agent content as JSON: {type(parsed_json)}"
                            )

                            # For JSON responses, we need to use format_execution_result but wrap the parsed JSON
                            # in the format it expects (a list containing the JSON object)
                            if isinstance(parsed_json, dict):
                                self.logger.debug(
                                    f"Agent returned JSON object, using flatten_agent_json_response"
                                )
                                from app.utils.helper_functions import (
                                    flatten_agent_json_response,
                                )

                                formatted_result = flatten_agent_json_response(
                                    parsed_json
                                )
                            elif isinstance(parsed_json, list):
                                self.logger.debug(
                                    f"Agent returned JSON array, using format_execution_result directly"
                                )
                                # For arrays, we can pass directly to format_execution_result
                                if (
                                    not output_schema
                                    or "predefined_fields" not in output_schema
                                ):
                                    formatted_result = format_execution_result(
                                        {}, parsed_json
                                    )
                                else:
                                    formatted_result = format_execution_result(
                                        output_schema, parsed_json
                                    )
                            else:
                                # Parsed JSON but it's a primitive type (string, number, boolean)
                                self.logger.debug(
                                    f"Agent returned JSON primitive: {type(parsed_json)}"
                                )

                                # Check if the primitive is actually a stringified JSON object
                                if isinstance(parsed_json, str) and parsed_json.strip():
                                    try:
                                        # Try to parse the string as JSON again
                                        double_parsed = json.loads(parsed_json)
                                        if isinstance(double_parsed, dict):
                                            self.logger.debug(
                                                f"JSON primitive was actually stringified JSON object, using flatten_agent_json_response"
                                            )
                                            from app.utils.helper_functions import (
                                                flatten_agent_json_response,
                                            )

                                            formatted_result = (
                                                flatten_agent_json_response(
                                                    double_parsed
                                                )
                                            )
                                        elif isinstance(double_parsed, list):
                                            self.logger.debug(
                                                f"JSON primitive was actually stringified JSON array, using format_execution_result"
                                            )
                                            if (
                                                not output_schema
                                                or "predefined_fields"
                                                not in output_schema
                                            ):
                                                formatted_result = (
                                                    format_execution_result(
                                                        {}, double_parsed
                                                    )
                                                )
                                            else:
                                                formatted_result = (
                                                    format_execution_result(
                                                        output_schema, double_parsed
                                                    )
                                                )
                                        else:
                                            # Still a primitive after double parsing
                                            formatted_result = {
                                                "data": double_parsed,
                                                "data_type": (
                                                    "string"
                                                    if isinstance(double_parsed, str)
                                                    else (
                                                        "number"
                                                        if isinstance(
                                                            double_parsed, (int, float)
                                                        )
                                                        else (
                                                            "boolean"
                                                            if isinstance(
                                                                double_parsed, bool
                                                            )
                                                            else "string"
                                                        )
                                                    )
                                                ),
                                                "semantic_type": "string",
                                                "property_name": "content",
                                            }
                                    except (json.JSONDecodeError, ValueError):
                                        # Not valid JSON after all, treat as regular string
                                        formatted_result = {
                                            "data": parsed_json,
                                            "data_type": "string",
                                            "semantic_type": "string",
                                            "property_name": "content",
                                        }
                                else:
                                    # Non-string primitive or empty string
                                    formatted_result = {
                                        "data": parsed_json,
                                        "data_type": (
                                            "string"
                                            if isinstance(parsed_json, str)
                                            else (
                                                "number"
                                                if isinstance(parsed_json, (int, float))
                                                else (
                                                    "boolean"
                                                    if isinstance(parsed_json, bool)
                                                    else "string"
                                                )
                                            )
                                        ),
                                        "semantic_type": "string",
                                        "property_name": "content",
                                    }
                        except (json.JSONDecodeError, ValueError) as e:
                            # Not valid JSON, treat as regular string
                            self.logger.debug(
                                f"Agent content is not valid JSON ({e}), treating as string"
                            )
                            formatted_result = {
                                "data": agent_content,
                                "data_type": "string",
                                "semantic_type": "string",
                                "property_name": "content",
                            }
                    else:
                        # Not a string or empty string, use the content as-is
                        self.logger.debug(
                            f"Agent content is not a string or is empty, using as-is"
                        )
                        formatted_result = {
                            "data": agent_content if agent_content is not None else "",
                            "data_type": "string",
                            "semantic_type": "string",
                            "property_name": "content",
                        }

                else:
                    formatted_result = execution_result

                if self.result_callback:
                    try:
                        result_info["result"] = formatted_result
                        result_info["status"] = "completed"
                        result_info["message"] = "Transition Result received."
                        result_info["raw_result"] = execution_result
                        result_info["approval_required"] = (
                            True
                            if approval_required
                            and self.approval
                            or (action_type == "regenerate")
                            else False
                        )
                        await self.result_callback(result_info)
                        if approval_required and self.approval:
                            self.state_manager.workflow_paused = True
                            self.logger.info(
                                f"Transition '{transition_id}' completed. Workflow paused, waiting for approval..."
                            )
                            result_info["result"] = (
                                "Workflow paused, waiting for approval..."
                            )
                            result_info["message"] = (
                                "Workflow paused, waiting for approval..."
                            )
                            result_info["status"] = "paused"
                            await self.result_callback(result_info)
                            self.state_manager.transitions_waiting_for_approval.append(
                                transition_id
                            )
                            self._pause_event.clear()
                            await self._pause_event.wait()
                            self.logger.info(
                                f"Workflow resumed after approval for transition '{transition_id}'."
                            )
                            del self.state_manager.transitions_waiting_for_approval[
                                transition_id
                            ]
                            self.state_manager.workflow_paused = False
                    except Exception as callback_error:
                        self.logger.error(
                            f"Result callback function raised an exception: {callback_error}"
                        )
                if action_type == "regenerate":
                    return []

            except Exception as e:
                self.logger.error(
                    f"Tool execution failed for tool '{tool_name}' (tool_id: {tool_id}) in node '{node_id}' of transition '{transition_id}': {str(e)}"
                    + traceback.format_exc()
                )
                error_message = f"[ERROR] Tool Execution Failed with error: {str(e)}"
                tool_results[tool_name] = error_message
                result_info["result"] = error_message
                result_info["message"] = "Transition faced an error during execution."
                result_info["status"] = "failed"
                if self.result_callback:
                    await self.result_callback(result_info)
                raise Exception(f"Tool execution error: {error_message}")

        # Handle loop completion specially
        if execution_type == "loop":
            # For loop transitions, mark as completed and continue with standard routing
            self.state_manager.mark_transition_completed(transition_id, tool_results)
            # Loop executor handles its own internal routing, continue with standard next transition logic

            # 🔍 LOOP STACK: Pop from stack when loop completes and get entry transitions to filter
            loop_body_entry_transitions = []
            if self._loop_execution_stack:
                popped_loop = self._loop_execution_stack.pop()
                loop_body_entry_transitions = popped_loop.get("entry_transitions", [])
                self.logger.info(
                    f"🔍 LOOP STACK: Popped loop {popped_loop.get('transition_id')} from stack. Stack size: {len(self._loop_execution_stack)}"
                )
                self.logger.info(
                    f"🔍 LOOP COMPLETION: Loop body entry transitions to filter: {loop_body_entry_transitions}"
                )
            else:
                self.logger.warning(
                    f"🔍 LOOP STACK: No loop to pop from stack for transition {transition_id}"
                )
                loop_body_entry_transitions = []

        # Standard completion handling for non-loop transitions
        self.state_manager.mark_transition_completed(transition_id, tool_results)

        chosen_next_transitions = []
        conditional_routing = transition.get("conditional_routing")
        conditional_nodes = set()

        # Handle new component-based conditional routing first
        component_routing_transitions = await self._handle_transition_routing(
            transition, execution_result
        )
        if component_routing_transitions:
            self.logger.info(
                f"Component-based routing returned transitions: {component_routing_transitions}"
            )
            chosen_next_transitions.extend(component_routing_transitions)

        # Legacy embedded routing is no longer supported
        elif conditional_routing:
            self.logger.warning(
                f"Legacy embedded conditional routing detected in transition {transition_id}. "
                "This is no longer supported. Please use conditional components instead."
            )

        transition_output_transitions = [
            config.get("to_transition_id")
            for config in output_data_configs
            if config.get("to_transition_id")
        ]
        final_next_transitions = set()

        # 🔍 DEBUG: Log output data processing
        transition_id = transition.get("id", "unknown")
        self.logger.debug(f"🔗 Processing output_data for {transition_id}")
        self.logger.debug(f"🔗 output_data_configs count: {len(output_data_configs)}")
        self.logger.debug(
            f"🔗 transition_output_transitions: {transition_output_transitions}"
        )
        self.logger.debug(f"🔗 chosen_next_transitions: {chosen_next_transitions}")
        self.logger.debug(f"🔗 conditional_nodes: {conditional_nodes}")

        # Add all transitions from routing evaluation (component or legacy)
        for transition_id in chosen_next_transitions:
            if transition_id:  # Ensure we don't add None values
                final_next_transitions.add(transition_id)

        # Add standard output transitions if they don't conflict with conditional routing
        for output_transition in transition_output_transitions:
            if output_transition not in conditional_nodes:
                # 🔍 LOOP COMPLETION CHECK: Filter out loop body entry transitions for completed loops
                if (
                    execution_type == "loop"
                    and loop_body_entry_transitions
                    and output_transition in loop_body_entry_transitions
                ):
                    self.logger.info(
                        f"🔍 LOOP COMPLETION: Filtering out loop body entry transition: {output_transition}"
                    )
                    continue

                final_next_transitions.add(output_transition)
                self.logger.debug(f"🔗 Added output transition: {output_transition}")

        next_transitions.extend(list(final_next_transitions))
        self.logger.debug(f"🔗 Final next_transitions: {next_transitions}")

        # 🔍 LOOP COMPLETION CHECK: Log final filtering result for completed loops
        if execution_type == "loop" and loop_body_entry_transitions:
            self.logger.info(
                f"🔍 LOOP COMPLETION: Final next_transitions after filtering: {next_transitions}"
            )

        return next_transitions

    async def _resolve_tool_parameters_universally(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        transition: dict,
    ) -> dict:
        """
        Universal tool parameter resolution using handle-based system.

        This method uses the new handle-based data propagation system to resolve
        tool parameters without hardcoded patterns or placeholder interpretation.

        Args:
            node_tool_info: Tool information from node definition
            input_data_configs: Input data configurations with handle mappings
            transition_id: Current transition ID
            tool_params_config: Tool parameter configuration
            transition: Complete transition object with result_resolution metadata

        Returns:
            dict: Resolved tool parameters
        """
        self.logger.debug(
            f"🔧 Starting universal parameter resolution for transition: {transition_id}"
        )

        # Extract iteration context if available (for loop execution)
        iteration_context = transition.get("iteration_context")
        if iteration_context:
            self.logger.debug(
                f"🔄 Found iteration context for loop execution: {iteration_context}"
            )

        # Check if we have result_resolution metadata for enhanced resolution
        result_resolution = transition.get("result_resolution", {})
        if result_resolution:
            self.logger.debug(
                f"📊 Using result_resolution metadata: {result_resolution.get('node_type', 'unknown')}"
            )

            # Use enhanced handle-based resolution with result_resolution metadata
            return await self._resolve_with_result_resolution(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
                result_resolution,
                iteration_context,
            )
        else:
            self.logger.debug(
                "⚠️ No result_resolution metadata found, falling back to standard handle resolution"
            )

            # Fallback to standard handle-based resolution
            return await self.workflow_utils._format_tool_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
                iteration_context,
            )

    async def _resolve_with_result_resolution(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        result_resolution: dict,
        iteration_context: dict = None,
    ) -> dict:
        """
        Enhanced parameter resolution using result_resolution metadata.

        This method uses the complete result_resolution system to provide
        the most accurate parameter resolution possible.

        Args:
            node_tool_info: Tool information from node definition
            input_data_configs: Input data configurations with handle mappings
            transition_id: Current transition ID
            tool_params_config: Tool parameter configuration
            result_resolution: Result resolution metadata from transition

        Returns:
            dict: Resolved tool parameters with enhanced accuracy
        """
        node_type = result_resolution.get("node_type", "unknown")
        self.logger.debug(f"🎯 Enhanced parameter resolution for {node_type} node")

        # Debug conditional detection
        is_conditional_node_type = node_type == "conditional"
        is_conditional_transition_id = "conditional" in transition_id.lower()
        self.logger.debug(
            f"🔀 Conditional detection: node_type={node_type}, is_conditional_node_type={is_conditional_node_type}, is_conditional_transition_id={is_conditional_transition_id}"
        )

        # Special handling for conditional components - they need node_output parameter
        if is_conditional_node_type or is_conditional_transition_id:
            self.logger.info(
                f"🔀 Using conditional parameter resolution for {transition_id}"
            )
            return await self._resolve_conditional_component_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
                result_resolution,
            )

        # Collect all previous results
        all_previous_results = {}
        if input_data_configs:
            for input_data_config in input_data_configs:
                from_transition_id = input_data_config.get("from_transition_id")
                if from_transition_id:
                    result_from_dependency = self.state_manager.get_transition_result(
                        from_transition_id
                    )
                    if result_from_dependency:
                        all_previous_results[from_transition_id] = (
                            result_from_dependency
                        )
                        self.logger.debug(
                            f"📥 Collected results from transition {from_transition_id}"
                        )

        # Add iteration context under the loop transition ID if available
        if iteration_context:
            loop_transition_id = iteration_context.get("transition_id")
            if loop_transition_id:
                self.logger.debug(
                    f"🔄 Adding iteration context for loop transition {loop_transition_id}: {iteration_context}"
                )
                # Store iteration context under the loop transition ID so handle mappings can find it
                all_previous_results[loop_transition_id] = {
                    "current_item": iteration_context.get("iteration_item"),
                    "iteration_index": iteration_context.get("iteration_index"),
                    "loop_id": iteration_context.get("loop_id"),
                }
            else:
                self.logger.warning(
                    "🔄 Iteration context missing transition_id, cannot store for handle mapping"
                )

        # If no previous results, convert current params to dict format
        if not all_previous_results:
            self.logger.debug("📝 No previous results found, using static parameters")
            return self.workflow_utils._convert_params_to_dict(tool_params_config)

        # Extract handle mappings from input data configs
        handle_mappings = self.workflow_utils._extract_handle_mappings(
            input_data_configs
        )

        if not handle_mappings:
            self.logger.debug(
                "🔄 No handle mappings found, falling back to standard resolution"
            )
            return await self.workflow_utils._format_tool_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
            )

        # Validate handle mapping compatibility
        validation_report = self.workflow_utils.validate_handle_mapping_compatibility(
            handle_mappings, all_previous_results
        )

        self.logger.info(
            f"🔍 Handle validation: {validation_report['overall_compatibility']} "
            f"({validation_report['compatible_mappings']}/{validation_report['total_mappings']} compatible)"
        )

        # Create universal parameter mapping
        parameter_mapping = self.workflow_utils.create_universal_parameter_mapping(
            handle_mappings, all_previous_results, iteration_context
        )

        resolved_parameters = parameter_mapping["resolved_parameters"]
        mapping_metadata = parameter_mapping["mapping_metadata"]

        # Log detailed mapping results
        self.logger.info(
            f"🎯 Parameter mapping complete: {mapping_metadata['successful_mappings']}/{mapping_metadata['total_mappings']} successful"
        )

        # If we have failed mappings, log details for debugging
        if mapping_metadata["failed_mappings"] > 0:
            failed_details = [
                detail
                for detail in mapping_metadata["mapping_details"]
                if detail["status"] in ["failed", "error"]
            ]
            for detail in failed_details:
                self.logger.warning(
                    f"❌ Failed mapping: {detail['source_handle_id']} → {detail['target_handle_id']} "
                    f"(Error: {detail.get('error', 'Unknown')})"
                )

        # Merge with static parameters that don't have handle mappings
        current_params_dict = self.workflow_utils._convert_params_to_dict(
            tool_params_config
        )

        # Extract system_message and input_variables from agent_config if present
        agent_config = current_params_dict.get("agent_config", {})
        if isinstance(agent_config, dict):
            # Move system_message to top-level if it exists in agent_config
            if (
                "system_message" in agent_config
                and "system_message" not in resolved_parameters
            ):
                resolved_parameters["system_message"] = agent_config["system_message"]
                self.logger.debug(
                    f"📌 Extracted system_message from agent_config: {agent_config['system_message']}"
                )

            # Move input_variables to top-level if it exists in agent_config
            if (
                "input_variables" in agent_config
                and "input_variables" not in resolved_parameters
            ):
                resolved_parameters["input_variables"] = agent_config["input_variables"]
                self.logger.debug(
                    f"📌 Extracted input_variables from agent_config: {agent_config['input_variables']}"
                )

            # Move query to top-level if it exists in agent_config
            if (
                "query" in agent_config
                and "query" not in resolved_parameters
            ):
                resolved_parameters["query"] = agent_config["query"]
                self.logger.debug(
                    f"📌 Extracted query from agent_config: {agent_config['query'][:100]}..."
                )

        # Also check for system_message as a top-level parameter (not just inside agent_config)
        if (
            "system_message" in current_params_dict
            and "system_message" not in resolved_parameters
        ):
            resolved_parameters["system_message"] = current_params_dict[
                "system_message"
            ]
            self.logger.debug(
                f"📌 Using top-level system_message: {current_params_dict['system_message']}"
            )

        # Also check for query as a top-level parameter (should be passed as-is to agent-platform)
        if (
            "query" in current_params_dict
            and "query" not in resolved_parameters
        ):
            resolved_parameters["query"] = current_params_dict["query"]
            self.logger.debug(
                f"📌 Using top-level query: {current_params_dict['query'][:100]}..."
            )

        for param_name, param_value in current_params_dict.items():
            if param_name not in resolved_parameters:
                # Only include if it's not a placeholder (no ${...} pattern)
                if not (
                    isinstance(param_value, str)
                    and "${" in param_value
                    and "}" in param_value
                ):
                    resolved_parameters[param_name] = param_value
                    self.logger.debug(
                        f"📌 Added static parameter: {param_name} = {param_value}"
                    )


        self.logger.debug(f"✅ Final resolved parameters: {resolved_parameters}")
        return resolved_parameters

    def _get_executor_for_type(self, execution_type, transition=None):
        """
        Returns the appropriate executor based on the execution type.

        Args:
            execution_type: Type of execution (loop, Components, agent, MCP)
            transition: Transition object (needed for loop stack management)
        """
        # MCP server executor types
        if execution_type == "MCP":
            return self.tool_executor

        # Node executor types
        elif execution_type == "Components":
            if self.node_executor:
                return self.node_executor
            else:
                self.logger.warning(
                    f"Node executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor

        # Agent executor types
        elif execution_type == "agent":
            if self.agent_executor:
                return self.agent_executor
            else:
                self.logger.warning(
                    f"Agent executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor

        # Loop executor types
        elif execution_type == "loop":
            try:
                # 🔍 LOOP STACK: Push loop configuration to stack when loop starts
                if transition:
                    loop_config = transition.get("loop_config", {})
                    loop_body_config = loop_config.get("loop_body_configuration", {})
                    loop_entry_transitions = loop_body_config.get(
                        "entry_transitions", []
                    )

                    loop_stack_entry = {
                        "transition_id": transition.get("id"),
                        "entry_transitions": loop_entry_transitions,
                    }
                    self._loop_execution_stack.append(loop_stack_entry)
                    self.logger.info(
                        f"🔍 LOOP STACK: Pushed loop {transition.get('id')} to stack. Stack size: {len(self._loop_execution_stack)}"
                    )

                # Create a new LoopExecutor instance for this execution
                loop_executor = LoopExecutor(
                    state_manager=self.state_manager,
                    workflow_utils=self.workflow_utils,
                    result_callback=self.result_callback,
                    transitions_by_id=self.transitions_by_id,
                    nodes=self.nodes,
                    transition_handler=self,
                    user_id=self.user_id,
                )

                # Set the orchestration engine reference for proper coordination
                if hasattr(self, "orchestration_engine") and self.orchestration_engine:
                    loop_executor.set_orchestration_engine(self.orchestration_engine)
                    self.logger.debug(f"🔗 Set orchestration engine for loop executor")
                return loop_executor
            except Exception as e:
                self.logger.error(
                    f"Failed to instantiate LoopExecutor for execution type: {execution_type}. Error: {str(e)}"
                )
                self.logger.warning(
                    f"Loop executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor

        # Workflow executor types
        elif execution_type == "workflow":
            if self.workflow_executor:
                return self.workflow_executor
            else:
                self.logger.warning(
                    f"Workflow executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor
        else:
            self.logger.warning(
                f"Unknown execution type: {execution_type}. Falling back to MCP executor."
            )
            return self.tool_executor

    async def _handle_reflection_logic(self, transition: dict) -> list[str]:
        """
        Handles reflection logic.
        """
        reflection_info = transition.get("reflection", {})
        next_transition_candidates = []

        if not reflection_info:
            next_transition_candidates = (
                await self._execute_standard_or_reflection_transition(transition)
            )
            return next_transition_candidates

        iteration_count = reflection_info.get("iteration_count", 0) + 1
        max_iterations = reflection_info.get("max_iterations", 1)
        reflection_info["iteration_count"] = iteration_count

        self.logger.info(
            f"[REFLECTION] transition '{transition['id']}' iteration {iteration_count}/{max_iterations}."
        )

        if iteration_count > max_iterations:
            self.logger.info(
                f"Max iterations {max_iterations} reached. Reflection ends."
            )
            next_transition_candidates = (
                await self._execute_standard_or_reflection_transition(transition)
            )
            return next_transition_candidates

        # Execute the reflection logic
        next_transition_candidates = (
            await self._execute_standard_or_reflection_transition(transition)
        )
        return next_transition_candidates

    def _resolve_next_transition(self, next_transition_candidates):
        """
        Resolve next transitions from transition candidates, prioritizing reflection transitions and then handling others based on closest sequence.
        Now directly handles a list of transition IDs as input (next_transition_candidates).
        Returns a list of ALL transition IDs to be executed next.
        Resolution logic:
        1. Prioritize reflection transitions.
        2. For non-reflection transitions, choose based on closest *higher* sequence number than current_transition.
        """
        if not next_transition_candidates:
            self.logger.info(
                "No next transition candidates provided for resolution. Returning empty list."
            )
            return []

        reflection_transitions = []
        non_reflection_transitions = []
        resolved_transition_ids = set()

        for transition_id in next_transition_candidates:
            if transition_id in resolved_transition_ids:
                continue

            transition = self.transitions_by_id.get(transition_id)
            if not transition:
                self.logger.warning(
                    f"Transition ID '{transition_id}' from candidates not found in transitions_by_id."
                )
                continue

            if transition["transition_type"] == "reflection":
                reflection_transitions.append(transition_id)
            else:
                non_reflection_transitions.append(transition_id)
            resolved_transition_ids.add(transition_id)

        prioritized_transitions = reflection_transitions + non_reflection_transitions
        self.logger.info(
            f"Resolved next transitions (direct transition IDs): {prioritized_transitions}"
        )
        return prioritized_transitions

    async def regenerate_transition(
        self, transition_id, action_type, server_params_override=None
    ):
        """
        Regenerates a specific transition (server) and its dependent transitions.
        Resets the state for the transition and re-executes it, then returns.
        Does NOT automatically resume the entire workflow execution flow.
        """
        if transition_id not in self.transitions_by_id:
            self.logger.error(
                f"Transition ID '{transition_id}' not found for regeneration: {transition_id}"
            )
            return False

        self.logger.info(f"Initiating regeneration for transition: {transition_id}")
        action_type = None
        if action_type == "re-execute":
            # 1. Reset workflow state for the given transition and its dependents
            reset_success = self.state_manager.reset_to_transition(
                transition_id, self.transitions_by_id, self.dependency_map
            )
            action_type = "re-execute"
            if not reset_success:
                self.logger.error(
                    f"State reset failed for transition {transition_id}, regeneration cannot proceed."
                )
                return False
        else:
            action_type = "regenerate"

        # 2. Get the transition object
        transition_to_regenerate = self.transitions_by_id[transition_id]

        original_approval_flag = self.approval
        self.approval = False

        # 3. Execute the transition directly, passing server_params_override
        result = await self._execute_standard_or_reflection_transition(
            transition_to_regenerate,
            server_params_override=server_params_override,
            action_type=action_type,
        )

        self.approval = original_approval_flag

        if result is not None:
            self.logger.info(f"Regeneration completed for transition: {transition_id}")
            return True
        else:
            self.logger.error(f"Regeneration failed for transition: {transition_id}")
            return False

    def _is_conditional_component_transition(self, transition: dict) -> bool:
        """
        Check if transition uses conditional component.

        Args:
            transition: Transition configuration dict

        Returns:
            True if transition uses conditional component, False otherwise
        """
        node_info = transition.get("node_info", {})
        tools_to_use = node_info.get("tools_to_use", [])

        return any(tool.get("tool_name") == "conditional" for tool in tools_to_use)

    async def _handle_conditional_component_result(
        self, execution_result: dict, transition: dict
    ) -> list[str]:
        """
        Handle conditional component execution result and determine next transitions.

        Args:
            execution_result: The execution result from conditional component
            transition: The transition configuration that was executed

        Returns:
            List of next transition IDs to execute
        """
        return await self.conditional_routing_handler.handle_conditional_result(
            execution_result, transition, self.state_manager
        )

    def _extract_source_from_tool_params(self, tool_params_config: dict) -> str:
        """
        Extract the source field value from tool_params configuration.

        Args:
            tool_params_config: Tool parameters configuration

        Returns:
            Source value ('node_output', 'global_context', or 'node_output' as default)
        """
        self.logger.info(
            f"🔀 EXTRACT_SOURCE_METHOD_CALLED with config: {tool_params_config}"
        )

        # Handle both dict with "items" key and direct list format
        if isinstance(tool_params_config, list):
            items = tool_params_config
        elif isinstance(tool_params_config, dict) and "items" in tool_params_config:
            items = tool_params_config.get("items", [])
        else:
            self.logger.debug(
                "🔀 No valid tool_params_config format, using default: node_output"
            )
            return "node_output"  # Default source
        self.logger.debug(f"🔀 Searching for source in {len(items)} tool_params items")

        for item in items:
            field_name = item.get("field_name")
            self.logger.debug(f"🔀 Checking item with field_name: {field_name}")
            if field_name == "source":
                source = item.get("field_value", "node_output")
                self.logger.debug(f"🔀 Found source in tool_params: {source}")
                return source

        self.logger.debug(
            "🔀 No source found in tool_params, using default: node_output"
        )
        return "node_output"  # Default source

    async def _resolve_conditional_component_parameters(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        result_resolution: dict,
    ) -> dict:
        """
        Resolve parameters specifically for conditional components.

        Conditional components require proper data flow and evaluation parameters
        based on the source configuration.

        Args:
            node_tool_info: Tool information from node configuration
            input_data_configs: Input data configurations
            transition_id: Current transition ID
            tool_params_config: Tool parameters configuration
            result_resolution: Result resolution configuration

        Returns:
            Resolved parameters dictionary for conditional component
        """
        self.logger.debug(
            f"🔀 Resolving conditional component parameters for {transition_id}"
        )

        # Extract source from tool_params to determine evaluation strategy
        self.logger.info(f"🔀 BEFORE source extraction for {transition_id}")
        source = self._extract_source_from_tool_params(tool_params_config)
        self.logger.info(f"🔀 AFTER source extraction for {transition_id}: {source}")
        self.logger.info(
            f"🔀 Conditional component {transition_id} using source: {source}"
        )

        # Start with static parameters
        resolved_parameters = self.workflow_utils._convert_params_to_dict(
            tool_params_config
        )

        # Get input data from previous transition results (for data flow)
        input_data_for_flow = None

        if input_data_configs:
            # Look for input data from previous transitions
            for input_data_config in input_data_configs:
                from_transition_id = input_data_config.get("from_transition_id")
                if from_transition_id:
                    result_from_dependency = self.state_manager.get_transition_result(
                        from_transition_id
                    )
                    if result_from_dependency:
                        # Extract the actual output data from the nested structure
                        if isinstance(result_from_dependency, dict):
                            # The result is wrapped as {"result": {"result": actual_data}}
                            # First try to get the nested result.result
                            result_data = result_from_dependency.get("result", {})
                            if (
                                isinstance(result_data, dict)
                                and "result" in result_data
                            ):
                                input_data_for_flow = result_data["result"]
                            else:
                                # Fallback to just result
                                input_data_for_flow = result_data
                        else:
                            input_data_for_flow = result_from_dependency

                        self.logger.debug(
                            f"🔀 Found input data from transition {from_transition_id}: {type(input_data_for_flow)}"
                        )
                        break

        # If no input data found from previous transitions, try to get from user input for initial conditionals
        if input_data_for_flow is None:
            user_input_data = self._extract_user_input_from_tool_params(
                tool_params_config
            )
            if user_input_data:
                input_data_for_flow = user_input_data
                self.logger.debug(
                    f"🔀 Using user input data for initial conditional {transition_id}: {input_data_for_flow}"
                )
            else:
                input_data_for_flow = {}
                self.logger.debug(
                    f"🔀 No input data found for conditional component {transition_id}, using empty dict"
                )

        # Separate flow data from evaluation data
        flow_data = input_data_for_flow  # Always from previous node connection
        evaluation_data = None

        # Determine evaluation data based on source
        if source == "node_output":
            # For node_output source: use connected data for evaluation
            if not input_data_for_flow and "global_context" in resolved_parameters:
                # If no connected data, extract user input from global_context
                global_context_data = resolved_parameters.get("global_context", {})
                if global_context_data and "input_data" in global_context_data:
                    evaluation_data = global_context_data["input_data"]
                    # Also use this as flow data if no connection exists
                    flow_data = evaluation_data
                    self.logger.debug(
                        f"🔀 No connected data, using user input for both flow and evaluation: {evaluation_data}"
                    )
                else:
                    evaluation_data = input_data_for_flow
            else:
                evaluation_data = input_data_for_flow
            self.logger.debug(
                f"🔀 node_output source - flow_data: {flow_data}, evaluation_data: {evaluation_data}"
            )

        elif source == "global_context":
            # For global_context source: use global context for evaluation, but keep flow data separate
            global_context_data = {}

            # Get existing global_context if present
            if "global_context" in resolved_parameters:
                global_context_data = resolved_parameters.get("global_context", {})

            # For initial transitions, add user input to global_context
            user_input_for_global = self._get_user_input_for_initial_transition(
                transition_id
            )
            if user_input_for_global:
                global_context_data.update(user_input_for_global)
                self.logger.info(
                    f"🔀 Added user input to global_context for initial conditional {transition_id}: {user_input_for_global}"
                )

            # Set evaluation data to global context variables
            evaluation_data = global_context_data
            # Keep flow_data as the connected data (unchanged)
            self.logger.debug(
                f"🔀 global_context source - flow_data: {flow_data}, evaluation_data: {evaluation_data}"
            )

        else:
            # Fallback for unknown source (backward compatibility)
            self.logger.warning(
                f"🔀 Unknown source '{source}' for {transition_id}, falling back to node_output behavior"
            )
            evaluation_data = input_data_for_flow
            self.logger.debug(
                f"🔀 fallback source - flow_data: {flow_data}, evaluation_data: {evaluation_data}"
            )

        # Set unified payload with separated concerns
        resolved_parameters["input_data"] = flow_data  # Always connected data for flow
        resolved_parameters["evaluation_data"] = (
            evaluation_data  # Source-based data for evaluation
        )

        # Remove old fields for clean unified approach
        if "global_context" in resolved_parameters:
            del resolved_parameters["global_context"]
        if "node_output" in resolved_parameters:
            del resolved_parameters["node_output"]

        self.logger.debug(
            f"🔀 Unified payload - input_data (flow): {flow_data}, evaluation_data: {evaluation_data}, source: {source}"
        )

        # Ensure default_transition is present
        if "default_transition" not in resolved_parameters:
            # Generate a default transition ID based on the current transition
            # This should be a transition that handles the "no conditions matched" case
            default_transition_id = f"{transition_id}_default"
            resolved_parameters["default_transition"] = default_transition_id
            self.logger.debug(
                f"🔀 Generated default_transition: {default_transition_id}"
            )

        self.logger.info(
            f"🔀 Resolved conditional parameters: {list(resolved_parameters.keys())}"
        )

        return resolved_parameters

    def _extract_user_input_from_tool_params(self, tool_params_config: dict) -> dict:
        """
        Extract user input data from tool parameters for initial conditional transitions.

        This method looks for user-provided values in the tool parameters and
        constructs a node_output dictionary that the conditional component can use.

        Args:
            tool_params_config: Tool parameters configuration

        Returns:
            Dictionary containing user input data, or empty dict if none found
        """
        user_input = {}

        if not tool_params_config or "items" not in tool_params_config:
            return user_input

        # Look through tool parameter items for user-provided values
        for item in tool_params_config.get("items", []):
            field_name = item.get("field_name")
            field_value = item.get("field_value")

            # If field has a non-null value, include it in user input
            if field_name and field_value is not None and field_value != "":
                user_input[field_name] = field_value
                self.logger.debug(
                    f"🔀 Found user input field: {field_name} = {field_value}"
                )

        return user_input

    def _get_user_input_for_initial_transition(self, transition_id: str) -> dict:
        """
        Get user input data for initial transitions from the workflow execution context.

        This method attempts to retrieve user input data that was provided when the
        workflow was started, specifically for initial transitions that need user data.

        Args:
            transition_id: The transition ID to get user input for

        Returns:
            Dictionary containing user input data, or empty dict if none found
        """
        try:
            # Try to access user input from the workflow execution context
            self.logger.debug(
                f"🔀 Attempting to find user input for transition {transition_id}"
            )

            # Method 1: Check if we have access to the workflow engine through the state manager
            if (
                hasattr(self.state_manager, "workflow_engine")
                and self.state_manager.workflow_engine
            ):
                workflow_engine = self.state_manager.workflow_engine
                self.logger.debug(f"🔀 Found workflow_engine: {type(workflow_engine)}")

                if hasattr(workflow_engine, "user_payload_template"):
                    user_payload = workflow_engine.user_payload_template
                    self.logger.debug(f"🔀 Found user_payload_template: {user_payload}")

                    # Extract user input data from the payload template
                    if user_payload and isinstance(user_payload, dict):
                        # Look for the field that corresponds to this transition
                        for field_name, field_data in user_payload.items():
                            if isinstance(field_data, dict):
                                field_transition_id = field_data.get("transition_id")
                                field_value = field_data.get("value")

                                self.logger.debug(
                                    f"🔀 Checking field {field_name}: transition_id={field_transition_id}, value={field_value}"
                                )

                                # Check if this field is for the current transition
                                if field_transition_id and transition_id.endswith(
                                    field_transition_id
                                ):
                                    self.logger.info(
                                        f"🔀 Found user input for {transition_id}: {field_name} = {field_value}"
                                    )
                                    return {field_name: field_value}
                else:
                    self.logger.debug(
                        "🔀 workflow_engine has no user_payload_template attribute"
                    )
            else:
                self.logger.debug("🔀 No workflow_engine found in state_manager")

            # Method 2: Try to access through workflow execution context
            # Check if we have access to the workflow execution context through other means
            if hasattr(self, "workflow_context") and self.workflow_context:
                self.logger.debug("🔀 Found workflow_context, checking for user input")
                user_payload = getattr(
                    self.workflow_context, "user_payload_template", None
                )
                if user_payload:
                    self.logger.debug(
                        f"🔀 Found user_payload_template in workflow_context: {user_payload}"
                    )
                    # Process the user payload similar to Method 1
                    if isinstance(user_payload, dict):
                        # First check for direct field matches (old format)
                        for field_name, field_data in user_payload.items():
                            if isinstance(field_data, dict):
                                field_transition_id = field_data.get("transition_id")
                                field_value = field_data.get("value")
                                original_field_name = field_data.get("field_name", field_name)

                                self.logger.debug(
                                    f"🔀 Checking field {field_name}: transition_id={field_transition_id}, value={field_value}"
                                )

                                # Check if this is a new format entry with unique key
                                if "_" in field_name and "field_name" in field_data:
                                    # Use the original field name from the field_data
                                    field_name = original_field_name
                                    self.logger.debug(
                                        f"🔀 Using original field name from new format: {field_name}"
                                    )

                                if field_transition_id and transition_id.endswith(
                                    field_transition_id
                                ):
                                    self.logger.info(
                                        f"🔀 Found user input for {transition_id}: {field_name} = {field_value}"
                                    )
                                    return {field_name: field_value}
                else:
                    self.logger.debug(
                        "🔀 No user_payload_template found in workflow_context"
                    )
            else:
                self.logger.debug("🔀 No workflow_context found")

            # Method 3: Try to access through global workflow state (if available)
            # This could be implemented if there's a global registry of workflow data

            self.logger.debug(
                f"🔀 No user input found for initial transition {transition_id}"
            )
            return {}

        except Exception as e:
            self.logger.warning(f"🔀 Error getting user input for {transition_id}: {e}")
            import traceback

            self.logger.debug(f"🔀 Traceback: {traceback.format_exc()}")
            return {}

    async def _handle_transition_routing(
        self, transition: dict, execution_result: any
    ) -> list[str]:
        """
        Handle both legacy embedded routing and new component-based routing.

        Args:
            transition: Transition configuration dict
            execution_result: Result from transition execution

        Returns:
            List of next transition IDs to execute
        """
        transition_id = transition.get("id", "unknown")
        self.logger.debug(f"🔀 _handle_transition_routing called for {transition_id}")
        self.logger.debug(f"🔀 Execution result type: {type(execution_result)}")
        self.logger.debug(
            f"🔀 Execution result keys: {list(execution_result.keys()) if isinstance(execution_result, dict) else 'not dict'}"
        )

        # Check for legacy embedded routing (no longer supported)
        conditional_routing = transition.get("conditional_routing")
        if conditional_routing:
            self.logger.warning(
                f"🔀 Legacy embedded routing detected for {transition_id}. "
                "This is no longer supported. Please use conditional components instead."
            )
            return []

        # Check for new component-based routing
        is_conditional_result = (
            self.conditional_routing_handler.is_conditional_component_result(
                execution_result
            )
        )
        is_conditional_transition = self._is_conditional_component_transition(
            transition
        )

        self.logger.debug(f"🔀 Component routing check for {transition_id}:")
        self.logger.debug(f"🔀   - is_conditional_result: {is_conditional_result}")
        self.logger.debug(
            f"🔀   - is_conditional_transition: {is_conditional_transition}"
        )

        if is_conditional_result and is_conditional_transition:
            self.logger.info(
                f"🔀 Processing component-based conditional routing for {transition_id}"
            )
            # New component-based routing
            result = await self._handle_conditional_component_result(
                execution_result, transition
            )
            self.logger.info(f"🔀 Component routing returned: {result}")
            return result

        # No routing logic found
        self.logger.debug(
            f"🔀 No routing logic found for {transition_id}, returning empty list"
        )
        return []
